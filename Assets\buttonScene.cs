using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;

public class buttonScene : MonoBehaviour
{
    public int sceneNumber;
    private Button button;
    
    void Start()
    {
        button = GetComponent<Button>();
        button.onClick.AddListener(LoadLevel);
        
        // Optional: Set button text if not set by LevelSelectManager
        if (GetComponentInChildren<Text>().text == string.Empty)
        {
            GetComponentInChildren<Text>().text = sceneNumber.ToString();
        }
    }

    void LoadLevel()
    {
        // Optional: Add loading screen here
        SceneManager.LoadScene(sceneNumber);
    }
}
