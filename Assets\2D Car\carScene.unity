%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0, g: 0, b: 0, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 1
    m_BakeBackend: 0
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 512
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 0
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ShowResolutionOverlay: 1
    m_ExportTrainingData: 0
  m_LightingDataAsset: {fileID: 0}
  m_UseShadowmask: 0
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &64274915
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 64274919}
  - component: {fileID: 64274918}
  - component: {fileID: 64274917}
  - component: {fileID: 64274916}
  - component: {fileID: 64274921}
  - component: {fileID: 64274920}
  - component: {fileID: 64274922}
  m_Layer: 0
  m_Name: carBody
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!50 &64274916
Rigidbody2D:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 64274915}
  m_BodyType: 0
  m_Simulated: 1
  m_UseFullKinematicContacts: 0
  m_UseAutoMass: 0
  m_Mass: 1
  m_LinearDrag: 0
  m_AngularDrag: 0.05
  m_GravityScale: 1
  m_Material: {fileID: 0}
  m_Interpolate: 0
  m_SleepingMode: 1
  m_CollisionDetection: 0
  m_Constraints: 0
--- !u!60 &64274917
PolygonCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 64274915}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 6200000, guid: b339c64c7e27a284fa58edda9eeb86bd, type: 2}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0.5, y: 0.5}
    oldSize: {x: 6.482, y: 1.8621495}
    newSize: {x: 1, y: 1}
    adaptiveTilingThreshold: 0.5
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  m_Points:
    m_Paths:
    - - {x: -2.006635, y: -0.7656511}
      - {x: -2.00347, y: -0.76248604}
      - {x: -1.9908097, y: -0.68019503}
      - {x: -1.9718194, y: -0.6105642}
      - {x: -1.9623244, y: -0.59473896}
      - {x: -1.933839, y: -0.5409333}
      - {x: -1.8958585, y: -0.4839626}
      - {x: -1.8800333, y: -0.4618073}
      - {x: -1.8230627, y: -0.4048366}
      - {x: -1.7439367, y: -0.35103095}
      - {x: -1.690131, y: -0.3225456}
      - {x: -1.6015098, y: -0.29406023}
      - {x: -1.5698595, y: -0.28773016}
      - {x: -1.4780734, y: -0.27823505}
      - {x: -1.4749082, y: -0.28140008}
      - {x: -1.370462, y: -0.29406023}
      - {x: -1.3483067, y: -0.3003903}
      - {x: -1.291336, y: -0.31938055}
      - {x: -1.2248702, y: -0.35103095}
      - {x: -1.2185401, y: -0.35736102}
      - {x: -1.1900548, y: -0.37635127}
      - {x: -1.1425792, y: -0.4111667}
      - {x: -1.1045986, y: -0.4523122}
      - {x: -1.0096475, y: -0.5915739}
      - {x: -1.0096475, y: -0.597904}
      - {x: -0.99382234, y: -0.63904953}
      - {x: -0.9811622, y: -0.7055153}
      - {x: -0.97483206, y: -0.7498259}
      - {x: -0.968502, y: -0.7498259}
      - {x: -0.97166705, y: -0.8542722}
      - {x: -0.9811622, y: -0.92390305}
      - {x: -0.97483206, y: -0.9302331}
      - {x: 1.3862872, y: -0.9302331}
      - {x: 1.3926172, y: -0.92390305}
      - {x: 1.3926172, y: -0.9049128}
      - {x: 1.3894522, y: -0.82262176}
      - {x: 1.3926172, y: -0.81945676}
      - {x: 1.4021124, y: -0.75615597}
      - {x: 1.4147725, y: -0.7086804}
      - {x: 1.4369278, y: -0.6485446}
      - {x: 1.4559181, y: -0.6137292}
      - {x: 1.4875685, y: -0.5630886}
      - {x: 1.5255489, y: -0.51244795}
      - {x: 1.5603644, y: -0.47763252}
      - {x: 1.6205001, y: -0.43332195}
      - {x: 1.6458204, y: -0.41433173}
      - {x: 1.7122862, y: -0.38268134}
      - {x: 1.7565968, y: -0.36685613}
      - {x: 1.8452178, y: -0.3478659}
      - {x: 1.8831984, y: -0.34470087}
      - {x: 1.933839, y: -0.34470087}
      - {x: 2.0287902, y: -0.35736102}
      - {x: 2.0889258, y: -0.3731862}
      - {x: 2.1205764, y: -0.3890114}
      - {x: 2.1617217, y: -0.4111667}
      - {x: 2.2471778, y: -0.46813738}
      - {x: 2.3009834, y: -0.518778}
      - {x: 2.3009834, y: -0.52194303}
      - {x: 2.3579543, y: -0.60106903}
      - {x: 2.3896046, y: -0.6675349}
      - {x: 2.4149249, y: -0.7688161}
      - {x: 2.421255, y: -0.8131267}
      - {x: 2.4275851, y: -0.8542722}
      - {x: 2.4339151, y: -0.86060226}
      - {x: 2.4497404, y: -0.86060226}
      - {x: 2.503546, y: -0.8542722}
      - {x: 2.6111574, y: -0.82262176}
      - {x: 2.6206524, y: -0.81945676}
      - {x: 2.6428077, y: -0.8131267}
      - {x: 2.652303, y: -0.8099616}
      - {x: 2.6744583, y: -0.80363154}
      - {x: 2.6839533, y: -0.80046654}
      - {x: 2.7061086, y: -0.7941364}
      - {x: 2.7377589, y: -0.7846413}
      - {x: 2.7915647, y: -0.7688161}
      - {x: 2.8010597, y: -0.7656511}
      - {x: 2.823215, y: -0.759321}
      - {x: 2.83271, y: -0.75615597}
      - {x: 2.8548653, y: -0.7498259}
      - {x: 2.8643606, y: -0.7466608}
      - {x: 2.8865159, y: -0.74033076}
      - {x: 2.8960109, y: -0.73716575}
      - {x: 2.9181662, y: -0.7308357}
      - {x: 2.9498165, y: -0.72134054}
      - {x: 2.990962, y: -0.7086804}
      - {x: 3.066923, y: -0.6865251}
      - {x: 3.1112335, y: -0.67702997}
      - {x: 3.149214, y: -0.65803975}
      - {x: 3.1618743, y: -0.59473896}
      - {x: 3.1903596, y: -0.47130242}
      - {x: 3.2061846, y: -0.3953415}
      - {x: 3.2125149, y: -0.3636911}
      - {x: 3.2315052, y: -0.24658464}
      - {x: 3.2410002, y: -0.1706237}
      - {x: 3.2410002, y: 0.07308432}
      - {x: 3.2315052, y: 0.12055991}
      - {x: 3.2093499, y: 0.15537533}
      - {x: 3.1871946, y: 0.18069565}
      - {x: 3.149214, y: 0.19335581}
      - {x: 1.1963848, y: 0.19019078}
      - {x: 1.1678995, y: 0.22184116}
      - {x: 1.1235889, y: 0.2566566}
      - {x: 1.0571231, y: 0.31046227}
      - {x: 0.93052155, y: 0.4180736}
      - {x: 0.4272803, y: 0.8453539}
      - {x: 0.32599905, y: 0.933975}
      - {x: -1.4527531, y: 0.933975}
      - {x: -1.4590831, y: 0.91814977}
      - {x: -1.4875685, y: 0.851684}
      - {x: -1.60784, y: 0.56683046}
      - {x: -1.769257, y: 0.19019078}
      - {x: -2.8675256, y: 0.19019078}
      - {x: -2.899176, y: 0.18702573}
      - {x: -2.9276612, y: 0.16803549}
      - {x: -2.9561467, y: 0.1363851}
      - {x: -2.9719718, y: 0.12055991}
      - {x: -3.0004573, y: 0.054094084}
      - {x: -3.0131173, y: -0.021866858}
      - {x: -3.0194473, y: -0.072507486}
      - {x: -3.0226126, y: -0.113653}
      - {x: -3.0226126, y: -0.29406023}
      - {x: -3.0194473, y: -0.3478659}
      - {x: -3.0162823, y: -0.39217645}
      - {x: -3.0099523, y: -0.45864227}
      - {x: -3.0036223, y: -0.51561296}
      - {x: -2.984632, y: -0.6517097}
      - {x: -2.978302, y: -0.6928552}
      - {x: -3.0036223, y: -0.7276706}
      - {x: -3.0067873, y: -0.72450554}
      - {x: -3.0479329, y: -0.72134054}
      - {x: -3.1998546, y: -0.7118454}
      - {x: -3.2410002, y: -0.7118454}
      - {x: -3.2410002, y: -0.9049128}
      - {x: -3.2125149, y: -0.9049128}
      - {x: -3.1682043, y: -0.90174776}
      - {x: -3.1175637, y: -0.8985827}
      - {x: -3.0732532, y: -0.8954177}
      - {x: -3.0194473, y: -0.8922526}
      - {x: -2.9719718, y: -0.8890876}
      - {x: -2.9371564, y: -0.917573}
      - {x: -2.9276612, y: -0.9302331}
      - {x: -1.9971398, y: -0.9302331}
      - {x: -1.9908097, y: -0.92390305}
      - {x: -1.9908097, y: -0.90174776}
      - {x: -2.00347, y: -0.838447}
--- !u!212 &64274918
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 64274915}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: e420260b1a60af648a939b619f93cc92, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!4 &64274919
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 64274915}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.27453852, y: 0.59359694, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1202996237}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!235 &64274920
WheelJoint2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 64274915}
  m_Enabled: 1
  serializedVersion: 4
  m_EnableCollision: 0
  m_ConnectedRigidBody: {fileID: 465193434}
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_AutoConfigureConnectedAnchor: 1
  m_Anchor: {x: -1.4819078, y: -1.2154975}
  m_ConnectedAnchor: {x: 0, y: 0}
  m_Suspension:
    m_DampingRatio: 0.7
    m_Frequency: 2
    m_Angle: 90
  m_UseMotor: 0
  m_Motor:
    m_MotorSpeed: 0
    m_MaximumMotorForce: 10000
--- !u!235 &64274921
WheelJoint2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 64274915}
  m_Enabled: 1
  serializedVersion: 4
  m_EnableCollision: 0
  m_ConnectedRigidBody: {fileID: 614168526}
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_AutoConfigureConnectedAnchor: 1
  m_Anchor: {x: 1.9481267, y: -1.123919}
  m_ConnectedAnchor: {x: 0, y: 0}
  m_Suspension:
    m_DampingRatio: 0.7
    m_Frequency: 2
    m_Angle: 90
  m_UseMotor: 0
  m_Motor:
    m_MotorSpeed: 0
    m_MaximumMotorForce: 10000
--- !u!114 &64274922
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 64274915}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 89b2f9b92dc40b347b65ef9ea2184731, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  frontwheel: {fileID: 64274921}
  backwheel: {fileID: 64274920}
  speedF: 1000
  speedB: -800
  torqueF: 10000
  torqueB: 10000
  TractionFront: 1
  TractionBack: 1
  carRotationSpeed: 70
--- !u!1 &123615184
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 123615187}
  - component: {fileID: 123615186}
  - component: {fileID: 123615185}
  m_Layer: 0
  m_Name: rOAD
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!60 &123615185
PolygonCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 123615184}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 6200000, guid: f4773c246a278b74bbb58a8aec299f93, type: 2}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0.5, y: 0.5}
    oldSize: {x: 41.11, y: 12.385191}
    newSize: {x: 1, y: 1}
    adaptiveTilingThreshold: 0.5
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  m_Points:
    m_Paths:
    - - {x: 20.273975, y: 2.94073}
      - {x: 19.912657, y: 3.0009499}
      - {x: 19.671778, y: 3.0009499}
      - {x: 19.49112, y: 2.94073}
      - {x: 19.450972, y: 2.8805103}
      - {x: 19.1298, y: 2.8805103}
      - {x: 18.728334, y: 2.9206567}
      - {x: 18.44731, y: 3.0009499}
      - {x: 18.20643, y: 3.1414626}
      - {x: 17.804966, y: 3.4024146}
      - {x: 17.363356, y: 3.763733}
      - {x: 17.122477, y: 3.8038795}
      - {x: 16.620646, y: 3.763733}
      - {x: 16.09874, y: 3.3622682}
      - {x: 15.898008, y: 3.3421948}
      - {x: 15.516617, y: 3.3823414}
      - {x: 15.275738, y: 3.603147}
      - {x: 15.115151, y: 3.9644654}
      - {x: 14.693613, y: 4.606809}
      - {x: 14.492881, y: 4.9681277}
      - {x: 14.452735, y: 5.2090063}
      - {x: 14.472808, y: 5.991863}
      - {x: 14.432661, y: 6.1925955}
      - {x: 13.469146, y: 6.1925955}
      - {x: 13.408926, y: 6.172522}
      - {x: 13.168047, y: 6.072156}
      - {x: 12.706363, y: 5.8112035}
      - {x: 12.5056305, y: 5.750984}
      - {x: 11.301235, y: 5.2090063}
      - {x: 10.879698, y: 5.028347}
      - {x: 10.759258, y: 4.9480543}
      - {x: 10.658892, y: 4.827615}
      - {x: 10.618745, y: 4.747322}
      - {x: 9.956328, y: 4.205344}
      - {x: 9.775669, y: 3.8038795}
      - {x: 9.494644, y: 3.6432936}
      - {x: 9.133326, y: 3.3221216}
      - {x: 8.73186, y: 3.2217555}
      - {x: 8.430762, y: 3.0611694}
      - {x: 7.1862206, y: 3.0611694}
      - {x: 7.126001, y: 3.101316}
      - {x: 6.2628517, y: 3.4425611}
      - {x: 5.700801, y: 3.6633668}
      - {x: 5.6205077, y: 3.68344}
      - {x: 4.7774315, y: 3.4827075}
      - {x: 4.4562597, y: 3.4024146}
      - {x: 4.135088, y: 3.281975}
      - {x: 3.9343555, y: 3.2217555}
      - {x: 3.8741357, y: 3.201682}
      - {x: 3.733623, y: 3.1615357}
      - {x: 3.6131837, y: 3.1213892}
      - {x: 3.2920117, y: 3.0611694}
      - {x: 2.4489355, y: 3.1615357}
      - {x: 2.2682765, y: 3.181609}
      - {x: 0.8631494, y: 3.181609}
      - {x: 0.662417, y: 3.3421948}
      - {x: 0.48175782, y: 3.4425611}
      - {x: 0.36131835, y: 3.4425611}
      - {x: -0.40146485, y: 3.4224877}
      - {x: -0.58212405, y: 3.4024146}
      - {x: -0.82300293, y: 3.3421948}
      - {x: -1.3449073, y: 3.201682}
      - {x: -2.067544, y: 3.181609}
      - {x: -2.147837, y: 3.2619019}
      - {x: -2.2482033, y: 3.3421948}
      - {x: -2.3887157, y: 3.4827075}
      - {x: -2.7901807, y: 3.522854}
      - {x: -3.2920117, y: 3.5429273}
      - {x: -3.6934767, y: 3.5630004}
      - {x: -3.8540626, y: 3.6432936}
      - {x: -4.034722, y: 3.7235866}
      - {x: -4.33582, y: 3.8038795}
      - {x: -4.8376513, y: 4.1451244}
      - {x: -4.8376513, y: 4.205344}
      - {x: -4.958091, y: 4.2856374}
      - {x: -5.0785303, y: 4.2856374}
      - {x: -5.560288, y: 4.1250515}
      - {x: -5.640581, y: 4.0648317}
      - {x: -6.0219727, y: 4.0648317}
      - {x: -6.062119, y: 4.0447583}
      - {x: -6.1624856, y: 3.8440259}
      - {x: -6.463584, y: 3.5830739}
      - {x: -7.0657816, y: 3.281975}
      - {x: -7.2464404, y: 3.181609}
      - {x: -7.547539, y: 3.2217555}
      - {x: -8.009224, y: 3.3221216}
      - {x: -8.290249, y: 3.3823414}
      - {x: -8.691714, y: 3.522854}
      - {x: -9.615083, y: 3.522854}
      - {x: -11.120576, y: 3.201682}
      - {x: -11.14065, y: 3.181609}
      - {x: -19.711924, y: 3.181609}
      - {x: -19.752071, y: 3.1414626}
      - {x: -19.772144, y: 3.021023}
      - {x: -19.852436, y: 2.1378002}
      - {x: -19.892584, y: 1.6761158}
      - {x: -19.93273, y: 1.2345045}
      - {x: -20.013023, y: 0.3312085}
      - {x: -20.093315, y: -0.5520142}
      - {x: -20.133463, y: -1.0136987}
      - {x: -20.213755, y: -1.8969214}
      - {x: -20.294048, y: -2.8002174}
      - {x: -20.394415, y: -3.9042456}
      - {x: -20.555, y: -5.6907644}
      - {x: -20.555, y: -6.1925955}
      - {x: 20.555, y: -6.1925955}
      - {x: 20.555, y: 2.8604372}
      - {x: 20.534927, y: 2.8805103}
--- !u!212 &123615186
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 123615184}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: cf9feac8c22170340bf5824c94ee217e, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!4 &123615187
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 123615184}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 8.1, y: -17.6, z: 0}
  m_LocalScale: {x: 1.5843112, y: 1.6430221, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &465193433
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 465193437}
  - component: {fileID: 465193436}
  - component: {fileID: 465193435}
  - component: {fileID: 465193434}
  m_Layer: 0
  m_Name: backwheel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!50 &465193434
Rigidbody2D:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 465193433}
  m_BodyType: 0
  m_Simulated: 1
  m_UseFullKinematicContacts: 0
  m_UseAutoMass: 0
  m_Mass: 1
  m_LinearDrag: 0
  m_AngularDrag: 0.05
  m_GravityScale: 1
  m_Material: {fileID: 0}
  m_Interpolate: 0
  m_SleepingMode: 1
  m_CollisionDetection: 0
  m_Constraints: 0
--- !u!58 &465193435
CircleCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 465193433}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 6200000, guid: dc8dd1c093e8fb149b2d8ab30b59bf8b, type: 2}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  serializedVersion: 2
  m_Radius: 1.062
--- !u!212 &465193436
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 465193433}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300002, guid: e420260b1a60af648a939b619f93cc92, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!4 &465193437
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 465193433}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -2.1714296, y: -0.92640305, z: 0}
  m_LocalScale: {x: 0.4629393, y: 0.4629393, z: 0.4629393}
  m_Children: []
  m_Father: {fileID: 1202996237}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &614168525
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 614168529}
  - component: {fileID: 614168528}
  - component: {fileID: 614168527}
  - component: {fileID: 614168526}
  m_Layer: 0
  m_Name: FRONT WHEEL
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!50 &614168526
Rigidbody2D:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 614168525}
  m_BodyType: 0
  m_Simulated: 1
  m_UseFullKinematicContacts: 0
  m_UseAutoMass: 0
  m_Mass: 1
  m_LinearDrag: 0
  m_AngularDrag: 0.05
  m_GravityScale: 1
  m_Material: {fileID: 0}
  m_Interpolate: 0
  m_SleepingMode: 1
  m_CollisionDetection: 0
  m_Constraints: 0
--- !u!58 &614168527
CircleCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 614168525}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 6200000, guid: dc8dd1c093e8fb149b2d8ab30b59bf8b, type: 2}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  serializedVersion: 2
  m_Radius: 1.062
--- !u!212 &614168528
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 614168525}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300002, guid: e420260b1a60af648a939b619f93cc92, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!4 &614168529
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 614168525}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 1.7385703, y: -0.571403, z: 0}
  m_LocalScale: {x: 0.4629393, y: 0.4629393, z: 0.4629393}
  m_Children: []
  m_Father: {fileID: 1202996237}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1202996236
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1202996237}
  m_Layer: 0
  m_Name: Car
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1202996237
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1202996236}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -11.2, y: -8.42, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 64274919}
  - {fileID: 465193437}
  - {fileID: 614168529}
  m_Father: {fileID: 0}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2013535721
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2013535726}
  - component: {fileID: 2013535725}
  - component: {fileID: 2013535724}
  - component: {fileID: 2013535723}
  - component: {fileID: 2013535722}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &2013535722
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2013535721}
  m_Enabled: 1
--- !u!124 &2013535723
Behaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2013535721}
  m_Enabled: 1
--- !u!92 &2013535724
Behaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2013535721}
  m_Enabled: 1
--- !u!20 &2013535725
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2013535721}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.1397059, g: 0.6796147, b: 1, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 1
  orthographic size: 14.83
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 0
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &2013535726
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2013535721}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 8.7, y: 0, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
