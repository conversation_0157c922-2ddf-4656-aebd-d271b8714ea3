%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: SpriteLighting
  m_Shader: {fileID: 4800000, guid: 0b80b6673c34e2845934ad94e497d69b, type: 3}
  m_ShaderKeywords: ETC1_EXTERNAL_ALPHA GLOWTEX_ON OUTBASE8DIR_ON
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _ColorRampTex:
        m_Texture: {fileID: 2800000, guid: 279657edc397ece4b8029c727adf6ddc, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ColorSwapTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DistortTex:
        m_Texture: {fileID: 2800000, guid: 7aad8c583ef292e48b06af0d1f2fab97, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FadeBurnTex:
        m_Texture: {fileID: 2800000, guid: 677cca399782dea41aedc1d292ecb67d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FadeTex:
        m_Texture: {fileID: 2800000, guid: 7aad8c583ef292e48b06af0d1f2fab97, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GlowTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineDistortTex:
        m_Texture: {fileID: 2800000, guid: 7aad8c583ef292e48b06af0d1f2fab97, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineTex:
        m_Texture: {fileID: 2800000, guid: 74087f6d03f233e4a8a142fa01f9e5cf, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _Alpha: 1
    - _AlphaCutoffValue: 0.25
    - _BlurHD: 0
    - _BlurIntensity: 10
    - _BumpAmount: 1
    - _ChromAberrAlpha: 0.4
    - _ChromAberrAmount: 1
    - _ClipUvDown: 0
    - _ClipUvLeft: 0
    - _ClipUvRight: 0
    - _ClipUvUp: 0
    - _ColorChangeLuminosity: 0
    - _ColorChangeTolerance: 0.25
    - _ColorRampLuminosity: 0
    - _ColorRampOutline: 0
    - _ColorSwapBlueLuminosity: 0.5
    - _ColorSwapGreenLuminosity: 0.5
    - _ColorSwapRedLuminosity: 0.5
    - _DistortAmount: 0.5
    - _DistortTexXSpeed: 5
    - _DistortTexYSpeed: 5
    - _FadeAmount: -0.1
    - _FadeBurnGlow: 2
    - _FadeBurnTransition: 0.075
    - _FadeBurnWidth: 0.025
    - _FishEyeUvAmount: 0.35
    - _FlickerAlpha: 0
    - _FlickerFreq: 0.2
    - _FlickerPercent: 0.05
    - _GhostColorBoost: 1
    - _GhostTransparency: 0
    - _GlitchAmount: 3
    - _Glow: 10
    - _GlowTexUsed: 0
    - _GradBlend: 1
    - _GrassManualAnim: 1
    - _GrassManualToggle: 0
    - _GrassSpeed: 2
    - _GrassWind: 20
    - _GreyscaleLuminosity: 0
    - _GreyscaleOutline: 0
    - _HandDrawnAmount: 10
    - _HandDrawnSpeed: 5
    - _HitEffectBlend: 1
    - _HitEffectGlow: 5
    - _HologramFlickerAlpha: 0
    - _HologramFlickerFreq: 0.2
    - _HologramStripesAlpha: 0.4
    - _HologramStripesAmount: 50
    - _HologramStripesFill: 0.4
    - _HologramStripesLuminosity: 1
    - _HologramStripesSpeed: 5
    - _HsvBright: 1
    - _HsvSaturation: 1
    - _HsvShift: 180
    - _InnerOutlineAlpha: 1
    - _InnerOutlineGlow: 1
    - _InnerOutlineThickness: 1
    - _MaxXUV: 1
    - _MaxYUV: 1
    - _Metallic: 0
    - _MinXUV: 0
    - _MinYUV: 0
    - _MotionBlurAngle: 0.1
    - _MotionBlurDist: 1.25
    - _NegativeAmount: 1
    - _OffsetUvX: 0
    - _OffsetUvY: 0
    - _Outline8Directions: 0
    - _OutlineAlpha: 1
    - _OutlineDistortAmount: 0.5
    - _OutlineDistortTexXSpeed: 5
    - _OutlineDistortTexYSpeed: 5
    - _OutlineDistortToggle: 0
    - _OutlineGlow: 1.5
    - _OutlineIsPixel: 0
    - _OutlinePixelWidth: 1
    - _OutlineTexGrey: 0
    - _OutlineTexToggle: 0
    - _OutlineTexXSpeed: 10
    - _OutlineTexYSpeed: 0
    - _OutlineWidth: 0.004
    - _PinchUvAmount: 0.35
    - _PixelateSize: 32
    - _PosterizeGamma: 0.75
    - _PosterizeNumColors: 8
    - _PosterizeOutline: 0
    - _RectSize: 1
    - _RotateUvAmount: 0
    - _RoundWaveSpeed: 2
    - _RoundWaveStrength: 0.7
    - _ShadowAlpha: 0.5
    - _ShadowX: 0.1
    - _ShadowY: -0.05
    - _ShakeUvSpeed: 2.5
    - _ShakeUvX: 1.5
    - _ShakeUvY: 1
    - _Smoothness: 0.1
    - _TextureScrollXSpeed: 1
    - _TextureScrollYSpeed: 0
    - _TwistUvAmount: 1
    - _TwistUvPosX: 0.5
    - _TwistUvPosY: 0.5
    - _TwistUvRadius: 0.75
    - _WaveAmount: 7
    - _WaveSpeed: 10
    - _WaveStrength: 7.5
    - _WaveX: 0
    - _WaveY: 0.5
    - _ZoomUvAmount: 0.5
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _ColorChangeNewCol: {r: 1, g: 1, b: 0, a: 1}
    - _ColorChangeTarget: {r: 1, g: 0, b: 0, a: 1}
    - _ColorSwapBlue: {r: 1, g: 1, b: 1, a: 1}
    - _ColorSwapGreen: {r: 1, g: 1, b: 1, a: 1}
    - _ColorSwapRed: {r: 1, g: 1, b: 1, a: 1}
    - _FadeBurnColor: {r: 1, g: 1, b: 0, a: 1}
    - _GlowColor: {r: 1, g: 1, b: 1, a: 1}
    - _GradBotLeftCol: {r: 0, g: 0, b: 1, a: 1}
    - _GradBotRightCol: {r: 0, g: 0, b: 1, a: 1}
    - _GradTopLeftCol: {r: 1, g: 0, b: 0, a: 1}
    - _GradTopRightCol: {r: 1, g: 0, b: 0, a: 1}
    - _GreyscaleTintColor: {r: 1, g: 1, b: 1, a: 1}
    - _HitEffectColor: {r: 1, g: 1, b: 1, a: 1}
    - _InnerOutlineColor: {r: 1, g: 0, b: 0, a: 1}
    - _OutlineColor: {r: 1, g: 1, b: 1, a: 1}
    - _ShadowColor: {r: 0, g: 0, b: 0, a: 1}
