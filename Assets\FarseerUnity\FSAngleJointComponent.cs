/*
* FarseerUnity based on Farseer Physics Engine port:
* Copyright (c) 2012 <PERSON> https://github.com/gabstv/Farseer-Unity3D
* 
* Original source Box2D:
* Copyright (c) 2011 <PERSON> http://farseerphysics.codeplex.com/
* 
* This software is provided 'as-is', without any express or implied 
* warranty.  In no event will the authors be held liable for any damages 
* arising from the use of this software. 
* Permission is granted to anyone to use this software for any purpose, 
* including commercial applications, and to alter it and redistribute it 
* freely, subject to the following restrictions: 
* 1. The origin of this software must not be misrepresented; you must not 
* claim that you wrote the original software. If you use this software 
* in a product, an acknowledgment in the product documentation would be 
* appreciated but is not required. 
* 2. Altered source versions must be plainly marked as such, and must not be 
* misrepresented as being the original software. 
* 3. This notice may not be removed or altered from any source distribution. 
*/
using UnityEngine;
using System.Collections;
using FarseerPhysics.Dynamics.Joints;
using FarseerPhysics.Dynamics;
using FarseerPhysics.Factories;

[AddComponentMenu("FarseerUnity/Dynamics/Joints/Angle Joint Component")]
public class FSAngleJointComponent : FSJointComponent
{
	protected AngleJoint joint;
	
	public float BiasFactor = 0.2f;
	public float Softness = 0f;
	
	public override void InitJoint ()
	{
		joint = JointFactory.CreateAngleJoint(FSWorldComponent.PhysicsWorld, BodyA.PhysicsBody, BodyB.PhysicsBody);
		joint.BiasFactor = BiasFactor;
		joint.Softness = Softness;
		joint.CollideConnected = CollideConnected;
		base.InitJoint ();
	}
	
	public override void OnDrawGizmos ()
	{
		if(BodyA == null || BodyB == null)
			return;
		Gizmos.color = Color.red;
		Gizmos.DrawLine(BodyA.transform.position, BodyB.transform.position);
		
		base.OnDrawGizmos ();
	}
}
