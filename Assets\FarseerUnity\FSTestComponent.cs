/*
* FarseerUnity based on Farseer Physics Engine port:
* Copyright (c) 2012 <PERSON> https://github.com/gabstv/Farseer-Unity3D
* 
* Original source Box2D:
* Copyright (c) 2011 <PERSON> http://farseerphysics.codeplex.com/
* 
* This software is provided 'as-is', without any express or implied 
* warranty.  In no event will the authors be held liable for any damages 
* arising from the use of this software. 
* Permission is granted to anyone to use this software for any purpose, 
* including commercial applications, and to alter it and redistribute it 
* freely, subject to the following restrictions: 
* 1. The origin of this software must not be misrepresented; you must not 
* claim that you wrote the original software. If you use this software 
* in a product, an acknowledgment in the product documentation would be 
* appreciated but is not required. 
* 2. Altered source versions must be plainly marked as such, and must not be 
* misrepresented as being the original software. 
* 3. This notice may not be removed or altered from any source distribution. 
*/
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using CatsintheSky.FarseerDebug;

public class FSTestComponent : MonoBehaviour
{
	private Test currentTest;
	
	private List<Test> allTests = new List<Test>();
	private int curTestID = 0;
	
	void Start()
	{
		//allTests.Add(new BridgeTest(this.transform));
		//
		allTests.Add(new TheoJansenTest(this.transform));
		allTests.Add(new BuoyancyTest(this.transform));
		allTests.Add(new CCDTest(this.transform));
		allTests.Add(new BridgeTest(this.transform));
		allTests.Add(new RagdollTest(this.transform));
		
		currentTest = allTests[curTestID];
		currentTest.Start();
	}
	void Update()
	{
		if(currentTest != null)
			currentTest.Update();
		if(Input.GetKeyDown(KeyCode.LeftArrow))
			ChangeTest(-1);
		else if(Input.GetKeyDown(KeyCode.RightArrow))
			ChangeTest(1);
	}
	void OnGUI()
	{
		if(currentTest != null)
			currentTest.OnGUI();
	}
	private void ChangeTest(int val)
	{
		currentTest.Stop();
		curTestID += val;
		if(curTestID < 0)
			curTestID = allTests.Count - 1;
		if(curTestID >= allTests.Count)
			curTestID = 0;
		currentTest = allTests[curTestID];
		currentTest.Start();
	}
}
